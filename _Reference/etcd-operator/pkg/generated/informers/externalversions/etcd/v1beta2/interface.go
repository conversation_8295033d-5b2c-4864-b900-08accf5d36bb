/*
Copyright 2018 The etcd-operator Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// This file was automatically generated by informer-gen

package v1beta2

import (
	internalinterfaces "github.com/coreos/etcd-operator/pkg/generated/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// EtcdBackups returns a EtcdBackupInformer.
	EtcdBackups() EtcdBackupInformer
	// EtcdClusters returns a EtcdClusterInformer.
	EtcdClusters() EtcdClusterInformer
	// EtcdRestores returns a EtcdRestoreInformer.
	EtcdRestores() EtcdRestoreInformer
}

type version struct {
	internalinterfaces.SharedInformerFactory
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory) Interface {
	return &version{f}
}

// EtcdBackups returns a EtcdBackupInformer.
func (v *version) EtcdBackups() EtcdBackupInformer {
	return &etcdBackupInformer{factory: v.SharedInformerFactory}
}

// EtcdClusters returns a EtcdClusterInformer.
func (v *version) EtcdClusters() EtcdClusterInformer {
	return &etcdClusterInformer{factory: v.SharedInformerFactory}
}

// EtcdRestores returns a EtcdRestoreInformer.
func (v *version) EtcdRestores() EtcdRestoreInformer {
	return &etcdRestoreInformer{factory: v.SharedInformerFactory}
}
