/*
Copyright 2018 The etcd-operator Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// This file was automatically generated by informer-gen

package v1beta2

import (
	etcd_v1beta2 "github.com/coreos/etcd-operator/pkg/apis/etcd/v1beta2"
	versioned "github.com/coreos/etcd-operator/pkg/generated/clientset/versioned"
	internalinterfaces "github.com/coreos/etcd-operator/pkg/generated/informers/externalversions/internalinterfaces"
	v1beta2 "github.com/coreos/etcd-operator/pkg/generated/listers/etcd/v1beta2"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
	time "time"
)

// EtcdRestoreInformer provides access to a shared informer and lister for
// EtcdRestores.
type EtcdRestoreInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1beta2.EtcdRestoreLister
}

type etcdRestoreInformer struct {
	factory internalinterfaces.SharedInformerFactory
}

// NewEtcdRestoreInformer constructs a new informer for EtcdRestore type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewEtcdRestoreInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				return client.EtcdV1beta2().EtcdRestores(namespace).List(options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				return client.EtcdV1beta2().EtcdRestores(namespace).Watch(options)
			},
		},
		&etcd_v1beta2.EtcdRestore{},
		resyncPeriod,
		indexers,
	)
}

func defaultEtcdRestoreInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewEtcdRestoreInformer(client, v1.NamespaceAll, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc})
}

func (f *etcdRestoreInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&etcd_v1beta2.EtcdRestore{}, defaultEtcdRestoreInformer)
}

func (f *etcdRestoreInformer) Lister() v1beta2.EtcdRestoreLister {
	return v1beta2.NewEtcdRestoreLister(f.Informer().GetIndexer())
}
