apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: etcd-backup
spec:
  schedule: "*/30 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: etcd-backup
            image: lachlane<PERSON>son/k8s-kubectl
            command:
            - /bin/sh
            - "-ec"
            - |
              sed -e "s|<NOW>|$(date '+%Y-%m-%d_%H:%M:%S')|g" /var/etcd_backup/backup_cr.yaml | kubectl create -f -
            volumeMounts:
            - name: backup-config
              mountPath: /var/etcd_backup
          restartPolicy: OnFailure
          volumes:
          - name: backup-config
            configMap:
              name: backup-config
