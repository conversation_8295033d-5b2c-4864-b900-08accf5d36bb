# Cluster TLS policy

Cluster TLS policy is configured on a per-cluster basis through the CR spec provided to etcd operator.

For etcd's TLS support and requirements, see the [etcd security model][etcd-security].
To learn about generating self-signed TLS certs, see [Generate self-signed certificates][self-signed].

## Static cluster TLS policy

Static TLS means keys/certs are generated by the user and passed to an operator.

This section will use the following YAML manifest to describe the spec. The example cluster manifest and certs can be found in the [example/tls/ directory][example-tls].

```yaml
apiVersion: "etcd.database.coreos.com/v1beta2"
kind: "EtcdCluster"
metadata:
  name: example
  namespace: default
spec:
  ...
  TLS:
    static:
      member:
        peerSecret: etcd-peer-tls
        serverSecret: etcd-server-tls
      operatorSecret: etcd-client-tls
```

### member.peerSecret

`member.peerSecret` contains pem-encoded private keys and x509 certificates for etcd peer communication.

The peer TLS assets should have the following:
- **peer.crt**: peer communication cert.
  The certificate should allow wildcard domain `*.${clusterName}.${namespace}.svc` and `*.${clusterName}.${namespace}.svc.${cluster_domain}`.
  The example shown above uses `*.example.default.svc` and `*.example.default.svc.cluster.local`.
- **peer.key**: peer communication key.
- **peer-ca.crt**: CA cert for this peer key-cert pair.

Create a secret using the `peer.key`, `peer.crt`, and `peer-ca.crt`:

```
$ kubectl create secret generic etcd-peer-tls --from-file=peer-ca.crt --from-file=peer.crt --from-file=peer.key
```

Once passed, etcd operator will mount this secret at `/etc/etcdtls/member/peer-tls/` for each etcd member pod in the cluster.

### member.serverSecret

`member.serverSecret` contains pem-encoded private keys and x509 certificates for etcd client communication on server side.

The client TLS assets should have the following:
- **server.crt**: etcd server's client communication cert.
  The certificate should allow wildcard domain `*.${clusterName}.${namespace}.svc`,
  `${clusterName}-client.${namespace}.svc`, and `localhost`.
  The example shown above uses `*.example.default.svc`, `example-client.default.svc`, and `localhost`.
  To use more DNS name or IP to access etcd server, please add it here.
- **server.key**: etcd server's client communication key.
- **server-ca.crt**: CA cert for validating the certs of etcd clients.

Create a secret using `server.key`, `server.crt`, and `server-ca.crt`:

```
$ kubectl create secret generic etcd-server-tls --from-file=server-ca.crt --from-file=server.crt --from-file=server.key
```

etcd operator will mount this secret at `/etc/etcdtls/member/server-tls/` for each etcd member pod in the cluster.

### operatorSecret

The operator must send client requests (such as snapshot, healthy check, add/remove member) to maintain this cluster.

`operatorSecret` contains pem-encoded private keys and x509 certificates for communicating with etcd server via client URL.

The operator's etcd TLS assets should have the following:
- **etcd-client.crt**: operator's etcd x509 client cert.
- **etcd-client.key**: operator's etcd x509 client key.
- **etcd-client-ca.crt**: CA cert for validating the certs of etcd members.
They correspond to the `--cert`,`--key`, and `--cacert` arguments of `etcdctl`.

Create a secret containing the `etcd-client.crt`, `etcd-client.key`, and `etcd-client-ca.crt`:

```
$ kubectl create secret generic etcd-client-tls --from-file=etcd-client-ca.crt --from-file=etcd-client.crt --from-file=etcd-client.key
```

Pass `etcd-client-tls` to the `operatorSecret` field.

### Access a secure etcd cluster

Assume a secure etcd cluster `example` is up and running.

To access the cluster, use the service `example-client.default.svc`, which matches the SAN of its certificates.

Assume the following certs are used:

```
etcd-client.crt
etcd-client.key
etcd-client-ca.crt
```

`etcd-client.crt` and `etcd-client.key` should be trusted by the etcd server's client CA `server-ca.crt`.

`server.crt` and `server.key` should be trusted by the etcd client CA `etcd-client-ca.crt`.

The following  example `etcdctl` command will list members from the secure etcd cluster. Run this command from within a Kubernetes Pod to access the service name:

```
$ ETCDCTL_API=3 etcdctl --endpoints=https://example-client.default.svc:2379 \
    --cert=etcd-client.crt --key=etcd-client.key --cacert=etcd-client-ca.crt \
    member list -w table
```


[etcd-security]: https://coreos.com/etcd/docs/latest/op-guide/security.html
[self-signed]: https://coreos.com/os/docs/latest/generate-self-signed-certificates.html
[example-tls]: ../../example/tls/
