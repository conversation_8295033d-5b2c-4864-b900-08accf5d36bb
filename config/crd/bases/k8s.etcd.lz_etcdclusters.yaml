---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: etcdclusters.k8s.etcd.lz
spec:
  group: k8s.etcd.lz
  names:
    kind: EtcdCluster
    listKind: EtcdClusterList
    plural: etcdclusters
    shortNames:
    - etcd
    singular: etcdcluster
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .spec.size
      name: Size
      type: integer
    - jsonPath: .status.size
      name: Ready
      type: integer
    - jsonPath: .spec.version
      name: Version
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: EtcdCluster is the Schema for the etcdclusters API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ClusterSpec defines the desired state of EtcdCluster
            properties:
              paused:
                default: false
                description: Paused is to pause the control of the operator for the
                  etcd cluster.
                type: boolean
              pod:
                description: |-
                  Pod defines the policy to create pod for the etcd pod.


                  Updating Pod does not take effect on any existing etcd pods.
                properties:
                  affinity:
                    description: The scheduling constraints on etcd pods.
                    properties:
                      nodeAffinity:
                        description: Describes node affinity scheduling rules for
                          the pod.
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              The scheduler will prefer to schedule pods to nodes that satisfy
                              the affinity expressions specified by this field, but it may choose
                              a node that violates one or more of the expressions. The node that is
                              most preferred is the one with the greatest sum of weights, i.e.
                              for each node that meets all of the scheduling requirements (resource
                              request, requiredDuringScheduling affinity expressions, etc.),
                              compute a sum by iterating through the elements of this field and adding
                              "weight" to the sum if the node matches the corresponding matchExpressions; the
                              node(s) with the highest sum are the most preferred.
                            items:
                              description: |-
                                An empty preferred scheduling term matches all objects with implicit weight 0
                                (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
                              properties:
                                preference:
                                  description: A node selector term, associated with
                                    the corresponding weight.
                                  properties:
                                    matchExpressions:
                                      description: A list of node selector requirements
                                        by node's labels.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      description: A list of node selector requirements
                                        by node's fields.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                weight:
                                  description: Weight associated with matching the
                                    corresponding nodeSelectorTerm, in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - preference
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              If the affinity requirements specified by this field are not met at
                              scheduling time, the pod will not be scheduled onto the node.
                              If the affinity requirements specified by this field cease to be met
                              at some point during pod execution (e.g. due to an update), the system
                              may or may not try to eventually evict the pod from its node.
                            properties:
                              nodeSelectorTerms:
                                description: Required. A list of node selector terms.
                                  The terms are ORed.
                                items:
                                  description: |-
                                    A null or empty node selector term matches no objects. The requirements of
                                    them are ANDed.
                                    The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
                                  properties:
                                    matchExpressions:
                                      description: A list of node selector requirements
                                        by node's labels.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchFields:
                                      description: A list of node selector requirements
                                        by node's fields.
                                      items:
                                        description: |-
                                          A node selector requirement is a selector that contains values, a key, and an operator
                                          that relates the key and values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              Represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                            type: string
                                          values:
                                            description: |-
                                              An array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. If the operator is Gt or Lt, the values
                                              array must have a single element, which will be interpreted as an integer.
                                              This array is replaced during a strategic merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  type: object
                                  x-kubernetes-map-type: atomic
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - nodeSelectorTerms
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      podAffinity:
                        description: Describes pod affinity scheduling rules (e.g.
                          co-locate this pod in the same node, zone, etc. as some
                          other pod(s)).
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              The scheduler will prefer to schedule pods to nodes that satisfy
                              the affinity expressions specified by this field, but it may choose
                              a node that violates one or more of the expressions. The node that is
                              most preferred is the one with the greatest sum of weights, i.e.
                              for each node that meets all of the scheduling requirements (resource
                              request, requiredDuringScheduling affinity expressions, etc.),
                              compute a sum by iterating through the elements of this field and adding
                              "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                              node(s) with the highest sum are the most preferred.
                            items:
                              description: The weights of all of the matched WeightedPodAffinityTerm
                                fields are added per-node to find the most preferred
                                node(s)
                              properties:
                                podAffinityTerm:
                                  description: Required. A pod affinity term, associated
                                    with the corresponding weight.
                                  properties:
                                    labelSelector:
                                      description: |-
                                        A label query over a set of resources, in this case pods.
                                        If it's null, this PodAffinityTerm matches with no Pods.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      description: |-
                                        MatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                        Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      description: |-
                                        MismatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                        Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      description: |-
                                        A label query over the set of namespaces that the term applies to.
                                        The term is applied to the union of the namespaces selected by this field
                                        and the ones listed in the namespaces field.
                                        null selector and null or empty namespaces list means "this pod's namespace".
                                        An empty selector ({}) matches all namespaces.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      description: |-
                                        namespaces specifies a static list of namespace names that the term applies to.
                                        The term is applied to the union of the namespaces listed in this field
                                        and the ones selected by namespaceSelector.
                                        null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      description: |-
                                        This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                        the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                        whose value of the label with key topologyKey matches that of any node on which any of the
                                        selected pods is running.
                                        Empty topologyKey is not allowed.
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  description: |-
                                    weight associated with matching the corresponding podAffinityTerm,
                                    in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              If the affinity requirements specified by this field are not met at
                              scheduling time, the pod will not be scheduled onto the node.
                              If the affinity requirements specified by this field cease to be met
                              at some point during pod execution (e.g. due to a pod label update), the
                              system may or may not try to eventually evict the pod from its node.
                              When there are multiple elements, the lists of nodes corresponding to each
                              podAffinityTerm are intersected, i.e. all terms must be satisfied.
                            items:
                              description: |-
                                Defines a set of pods (namely those matching the labelSelector
                                relative to the given namespace(s)) that this pod should be
                                co-located (affinity) or not co-located (anti-affinity) with,
                                where co-located is defined as running on a node whose value of
                                the label with key <topologyKey> matches that of any node on which
                                a pod of the set of pods is running
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      podAntiAffinity:
                        description: Describes pod anti-affinity scheduling rules
                          (e.g. avoid putting this pod in the same node, zone, etc.
                          as some other pod(s)).
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              The scheduler will prefer to schedule pods to nodes that satisfy
                              the anti-affinity expressions specified by this field, but it may choose
                              a node that violates one or more of the expressions. The node that is
                              most preferred is the one with the greatest sum of weights, i.e.
                              for each node that meets all of the scheduling requirements (resource
                              request, requiredDuringScheduling anti-affinity expressions, etc.),
                              compute a sum by iterating through the elements of this field and adding
                              "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                              node(s) with the highest sum are the most preferred.
                            items:
                              description: The weights of all of the matched WeightedPodAffinityTerm
                                fields are added per-node to find the most preferred
                                node(s)
                              properties:
                                podAffinityTerm:
                                  description: Required. A pod affinity term, associated
                                    with the corresponding weight.
                                  properties:
                                    labelSelector:
                                      description: |-
                                        A label query over a set of resources, in this case pods.
                                        If it's null, this PodAffinityTerm matches with no Pods.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      description: |-
                                        MatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                        Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      description: |-
                                        MismatchLabelKeys is a set of pod label keys to select which pods will
                                        be taken into consideration. The keys are used to lookup values from the
                                        incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                        to select the group of existing pods which pods will be taken into consideration
                                        for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value is empty.
                                        The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                        Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                        This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      description: |-
                                        A label query over the set of namespaces that the term applies to.
                                        The term is applied to the union of the namespaces selected by this field
                                        and the ones listed in the namespaces field.
                                        null selector and null or empty namespaces list means "this pod's namespace".
                                        An empty selector ({}) matches all namespaces.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: |-
                                              A label selector requirement is a selector that contains values, a key, and an operator that
                                              relates the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: |-
                                                  operator represents a key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                                type: string
                                              values:
                                                description: |-
                                                  values is an array of string values. If the operator is In or NotIn,
                                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                  the values array must be empty. This array is replaced during a strategic
                                                  merge patch.
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      description: |-
                                        namespaces specifies a static list of namespace names that the term applies to.
                                        The term is applied to the union of the namespaces listed in this field
                                        and the ones selected by namespaceSelector.
                                        null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    topologyKey:
                                      description: |-
                                        This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                        the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                        whose value of the label with key topologyKey matches that of any node on which any of the
                                        selected pods is running.
                                        Empty topologyKey is not allowed.
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  description: |-
                                    weight associated with matching the corresponding podAffinityTerm,
                                    in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: |-
                              If the anti-affinity requirements specified by this field are not met at
                              scheduling time, the pod will not be scheduled onto the node.
                              If the anti-affinity requirements specified by this field cease to be met
                              at some point during pod execution (e.g. due to a pod label update), the
                              system may or may not try to eventually evict the pod from its node.
                              When there are multiple elements, the lists of nodes corresponding to each
                              podAffinityTerm are intersected, i.e. all terms must be satisfied.
                            items:
                              description: |-
                                Defines a set of pods (namely those matching the labelSelector
                                relative to the given namespace(s)) that this pod should be
                                co-located (affinity) or not co-located (anti-affinity) with,
                                where co-located is defined as running on a node whose value of
                                the label with key <topologyKey> matches that of any node on which
                                a pod of the set of pods is running
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                    type: object
                  annotations:
                    additionalProperties:
                      type: string
                    description: |-
                      Annotations specifies the annotations to attach to pods the operator creates for the
                      etcd cluster.
                      The "etcd.version" annotation is reserved for the internal use of the etcd operator.
                    type: object
                  etcdEnv:
                    description: |-
                      List of environment variables to set in the etcd container.
                      This is used to configure etcd process. etcd cluster cannot be created, when
                      bad environement variables are provided. Do not overwrite any flags used to
                      bootstrap the cluster (for example `--initial-cluster` flag).
                      This field cannot be updated.
                    items:
                      description: EnvVar represents an environment variable present
                        in a Container.
                      properties:
                        name:
                          description: Name of the environment variable. Must be a
                            C_IDENTIFIER.
                          type: string
                        value:
                          description: |-
                            Variable references $(VAR_NAME) are expanded
                            using the previously defined environment variables in the container and
                            any service environment variables. If a variable cannot be resolved,
                            the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                            "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                            Escaped references will never be expanded, regardless of whether the variable
                            exists or not.
                            Defaults to "".
                          type: string
                        valueFrom:
                          description: Source for the environment variable's value.
                            Cannot be used if value is not empty.
                          properties:
                            configMapKeyRef:
                              description: Selects a key of a ConfigMap.
                              properties:
                                key:
                                  description: The key to select.
                                  type: string
                                name:
                                  description: |-
                                    Name of the referent.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    TODO: Add other useful fields. apiVersion, kind, uid?
                                  type: string
                                optional:
                                  description: Specify whether the ConfigMap or its
                                    key must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              description: |-
                                Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
                              properties:
                                apiVersion:
                                  description: Version of the schema the FieldPath
                                    is written in terms of, defaults to "v1".
                                  type: string
                                fieldPath:
                                  description: Path of the field to select in the
                                    specified API version.
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              description: |-
                                Selects a resource of the container: only resources limits and requests
                                (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
                              properties:
                                containerName:
                                  description: 'Container name: required for volumes,
                                    optional for env vars'
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: Specifies the output format of the
                                    exposed resources, defaults to "1"
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  description: 'Required: resource to select'
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              description: Selects a key of a secret in the pod's
                                namespace
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  description: |-
                                    Name of the referent.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    TODO: Add other useful fields. apiVersion, kind, uid?
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  labels:
                    additionalProperties:
                      type: string
                    description: |-
                      Labels specifies the labels to attach to pods the operator creates for the
                      etcd cluster.
                      "app" and "etcd_*" labels are reserved for the internal use of the etcd operator.
                      Do not overwrite them.
                    type: object
                  nodeSelector:
                    additionalProperties:
                      type: string
                    description: |-
                      NodeSelector specifies a map of key-value pairs. For the pod to be eligible
                      to run on a node, the node must have each of the indicated key-value pairs as
                      labels.
                    type: object
                  persistentVolumeClaimSpec:
                    description: |-
                      PersistentVolumeClaimSpec is the spec to describe PVC for the etcd container
                      This field is optional. If no PVC spec, etcd container will use emptyDir as volume
                    properties:
                      accessModes:
                        description: |-
                          accessModes contains the desired access modes the volume should have.
                          More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                      dataSource:
                        description: |-
                          dataSource field can be used to specify either:
                          * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)
                          * An existing PVC (PersistentVolumeClaim)
                          If the provisioner or an external controller can support the specified data source,
                          it will create a new volume based on the contents of the specified data source.
                          When the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,
                          and dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.
                          If the namespace is specified, then dataSourceRef will not be copied to dataSource.
                        properties:
                          apiGroup:
                            description: |-
                              APIGroup is the group for the resource being referenced.
                              If APIGroup is not specified, the specified Kind must be in the core API group.
                              For any other third-party types, APIGroup is required.
                            type: string
                          kind:
                            description: Kind is the type of resource being referenced
                            type: string
                          name:
                            description: Name is the name of resource being referenced
                            type: string
                        required:
                        - kind
                        - name
                        type: object
                        x-kubernetes-map-type: atomic
                      dataSourceRef:
                        description: |-
                          dataSourceRef specifies the object from which to populate the volume with data, if a non-empty
                          volume is desired. This may be any object from a non-empty API group (non
                          core object) or a PersistentVolumeClaim object.
                          When this field is specified, volume binding will only succeed if the type of
                          the specified object matches some installed volume populator or dynamic
                          provisioner.
                          This field will replace the functionality of the dataSource field and as such
                          if both fields are non-empty, they must have the same value. For backwards
                          compatibility, when namespace isn't specified in dataSourceRef,
                          both fields (dataSource and dataSourceRef) will be set to the same
                          value automatically if one of them is empty and the other is non-empty.
                          When namespace is specified in dataSourceRef,
                          dataSource isn't set to the same value and must be empty.
                          There are three important differences between dataSource and dataSourceRef:
                          * While dataSource only allows two specific types of objects, dataSourceRef
                            allows any non-core object, as well as PersistentVolumeClaim objects.
                          * While dataSource ignores disallowed values (dropping them), dataSourceRef
                            preserves all values, and generates an error if a disallowed value is
                            specified.
                          * While dataSource only allows local objects, dataSourceRef allows objects
                            in any namespaces.
                          (Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.
                          (Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                        properties:
                          apiGroup:
                            description: |-
                              APIGroup is the group for the resource being referenced.
                              If APIGroup is not specified, the specified Kind must be in the core API group.
                              For any other third-party types, APIGroup is required.
                            type: string
                          kind:
                            description: Kind is the type of resource being referenced
                            type: string
                          name:
                            description: Name is the name of resource being referenced
                            type: string
                          namespace:
                            description: |-
                              Namespace is the namespace of resource being referenced
                              Note that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.
                              (Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                            type: string
                        required:
                        - kind
                        - name
                        type: object
                      resources:
                        description: |-
                          resources represents the minimum resources the volume should have.
                          If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements
                          that are lower than previous value but must still be higher than capacity recorded in the
                          status field of the claim.
                          More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources
                        properties:
                          limits:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Limits describes the maximum amount of compute resources allowed.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                          requests:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Requests describes the minimum amount of compute resources required.
                              If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                              otherwise to an implementation-defined value. Requests cannot exceed Limits.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                        type: object
                      selector:
                        description: selector is a label query over volumes to consider
                          for binding.
                        properties:
                          matchExpressions:
                            description: matchExpressions is a list of label selector
                              requirements. The requirements are ANDed.
                            items:
                              description: |-
                                A label selector requirement is a selector that contains values, a key, and an operator that
                                relates the key and values.
                              properties:
                                key:
                                  description: key is the label key that the selector
                                    applies to.
                                  type: string
                                operator:
                                  description: |-
                                    operator represents a key's relationship to a set of values.
                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                  type: string
                                values:
                                  description: |-
                                    values is an array of string values. If the operator is In or NotIn,
                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                    the values array must be empty. This array is replaced during a strategic
                                    merge patch.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          matchLabels:
                            additionalProperties:
                              type: string
                            description: |-
                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                            type: object
                        type: object
                        x-kubernetes-map-type: atomic
                      storageClassName:
                        description: |-
                          storageClassName is the name of the StorageClass required by the claim.
                          More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1
                        type: string
                      volumeAttributesClassName:
                        description: |-
                          volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.
                          If specified, the CSI driver will create or update the volume with the attributes defined
                          in the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,
                          it can be changed after the claim is created. An empty string value means that no VolumeAttributesClass
                          will be applied to the claim but it's not allowed to reset this field to empty string once it is set.
                          If unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass
                          will be set by the persistentvolume controller if it exists.
                          If the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be
                          set to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource
                          exists.
                          More info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/
                          (Alpha) Using this field requires the VolumeAttributesClass feature gate to be enabled.
                        type: string
                      volumeMode:
                        description: |-
                          volumeMode defines what type of volume is required by the claim.
                          Value of Filesystem is implied when not included in claim spec.
                        type: string
                      volumeName:
                        description: volumeName is the binding reference to the PersistentVolume
                          backing this claim.
                        type: string
                    type: object
                  resources:
                    description: |-
                      Resources is the resource requirements for the etcd container.
                      This field cannot be updated once the cluster is created.
                    properties:
                      claims:
                        description: |-
                          Claims lists the names of resources, defined in spec.resourceClaims,
                          that are used by this container.


                          This is an alpha field and requires enabling the
                          DynamicResourceAllocation feature gate.


                          This field is immutable. It can only be set for containers.
                        items:
                          description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                          properties:
                            name:
                              description: |-
                                Name must match the name of one entry in pod.spec.resourceClaims of
                                the Pod where this field is used. It makes that resource available
                                inside a container.
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Limits describes the maximum amount of compute resources allowed.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Requests describes the minimum amount of compute resources required.
                          If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                          otherwise to an implementation-defined value. Requests cannot exceed Limits.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                    type: object
                  tolerations:
                    description: Tolerations specifies the pod's tolerations.
                    items:
                      description: |-
                        The pod this Toleration is attached to tolerates any taint that matches
                        the triple <key,value,effect> using the matching operator <operator>.
                      properties:
                        effect:
                          description: |-
                            Effect indicates the taint effect to match. Empty means match all taint effects.
                            When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
                          type: string
                        key:
                          description: |-
                            Key is the taint key that the toleration applies to. Empty means match all taint keys.
                            If the key is empty, operator must be Exists; this combination means to match all values and all keys.
                          type: string
                        operator:
                          description: |-
                            Operator represents a key's relationship to the value.
                            Valid operators are Exists and Equal. Defaults to Equal.
                            Exists is equivalent to wildcard for value, so that a pod can
                            tolerate all taints of a particular category.
                          type: string
                        tolerationSeconds:
                          description: |-
                            TolerationSeconds represents the period of time the toleration (which must be
                            of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
                            it is not set, which means tolerate the taint forever (do not evict). Zero and
                            negative values will be treated as 0 (evict immediately) by the system.
                          format: int64
                          type: integer
                        value:
                          description: |-
                            Value is the taint value the toleration matches to.
                            If the operator is Exists, the value should be empty, otherwise just a regular string.
                          type: string
                      type: object
                    type: array
                type: object
              repository:
                description: |-
                  Repository is the name of the repository that hosts
                  etcd container images. It should be direct clone of the repository in official
                  release:
                    https://github.com/coreos/etcd/releases
                  That means, it should have exact same tags and the same meaning for the tags.


                  By default, it is `quay.io/coreos/etcd`.
                type: string
              size:
                default: 3
                description: |-
                  Size is the expected size of the etcd cluster.
                  The etcd-operator will eventually make the size of the running
                  cluster equal to the expected size.
                  The vaild range of the size is from 1 to 7.
                maximum: 7
                minimum: 1
                type: integer
              version:
                default: 3.5.21
                description: |-
                  Version is the expected version of the etcd cluster.
                  The etcd-operator will eventually make the etcd cluster version
                  equal to the expected version.


                  The version must follow the [semver]( http://semver.org) format, for example "3.5.21".
                  Only etcd released versions are supported: https://github.com/coreos/etcd/releases


                  If version is not set, default is "3.5.21".
                pattern: ^[0-9]+\.[0-9]+\.[0-9]+$
                type: string
            required:
            - size
            type: object
          status:
            description: ClusterStatus defines the observed state of EtcdCluster
            properties:
              clientPort:
                description: |-
                  ClientPort is the port for etcd client to access.
                  It's the same on client LB service and etcd nodes.
                type: integer
              conditions:
                description: Condition keeps track of all cluster conditions, if they
                  exist.
                items:
                  description: ClusterCondition represents one current condition of
                    an etcd cluster.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      type: string
                    lastUpdateTime:
                      description: The last time this condition was updated.
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition.
                      type: string
                    reason:
                      description: The reason for the condition's last transition.
                      type: string
                    status:
                      description: Status of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of cluster condition.
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              controlPaused:
                description: ControlPaused indicates the operator pauses the control
                  of the cluster.
                type: boolean
              currentVersion:
                description: CurrentVersion is the current cluster version
                type: string
              members:
                description: Members are the etcd members in the cluster
                properties:
                  ready:
                    description: |-
                      Ready are the etcd members that are ready to serve requests
                      The member names are the same as the etcd pod names
                    items:
                      type: string
                    type: array
                  unready:
                    description: Unready are the etcd members not ready to serve requests
                    items:
                      type: string
                    type: array
                type: object
              phase:
                description: Phase is the cluster running phase
                type: string
              reason:
                description: Reason for the current phase
                type: string
              serviceName:
                description: ServiceName is the LB service for accessing etcd nodes.
                type: string
              size:
                description: Size is the current size of the cluster
                type: integer
              targetVersion:
                description: |-
                  TargetVersion is the version the cluster upgrading to.
                  If the cluster is not upgrading, TargetVersion is empty.
                type: string
            required:
            - currentVersion
            - members
            - phase
            - size
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
