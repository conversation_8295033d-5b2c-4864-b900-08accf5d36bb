apiVersion: k8s.etcd.lz/v1alpha1
kind: EtcdCluster
metadata:
  labels:
    app.kubernetes.io/name: etcd-k8s-operator
    app.kubernetes.io/managed-by: kustomize
  name: etcdcluster-sample
spec:
  # Size is the expected size of the etcd cluster.
  # The etcd-operator will eventually make the size of the running
  # cluster equal to the expected size.
  # The vaild range of the size is from 1 to 7.
  size: 3

  # Version is the expected version of the etcd cluster.
  # The version must follow the [semver]( http://semver.org) format, for example "3.5.21".
  # Only etcd released versions are supported: https://github.com/coreos/etcd/releases
  # If version is not set, default is "3.5.21".
  version: "3.5.21"

  # Repository is the name of the repository that hosts
  # etcd container images. It should be direct clone of the repository in official
  # release: https://github.com/coreos/etcd/releases
  # By default, it is `quay.io/coreos/etcd`.
  repository: "quay.io/coreos/etcd"

  # Pod defines the policy to create pod for the etcd pod.
  # Updating Pod does not take effect on any existing etcd pods.
  pod:
    resources:
      requests:
        cpu: "100m"
        memory: "128Mi"
      limits:
        cpu: "1000m"
        memory: "1Gi"
