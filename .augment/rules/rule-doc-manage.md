---
type: "always_apply"
---

# 文档更新规则

1. 每次完成重大进展，必须更新文档，如果没有进展，不需要更新，哪些文件能更新，必须遵守规范，防止创建过多文档
2. 文档必须放到 docs 目录下
3. README.md 文件是文档管理说明文件，若该文件记录的索引不对，要更新，所有文档都在docs目录下
4. 新建文件准则
    design下可以新建，文件名称必须包括中文说明
    my-self下禁止操作
    project-manage下不可以新建，除非我要求
    refactor只能新建Reference/etcd-operator 项目的分析说明文档，不能建其它文档

# 技术方案或重大修复记录更新要求

docs 目录下，design 文件夹存储开发技术方案文档，若实现了某个重要功能，或者修复了重大BUG，可以将实现方案，或者技术思路，问题分析过程记录到该目录下

# 项目管理文档更新要求

docs 目录下，project-manage 文件夹存储：项目主控文档，开发指南文档。项目有进展，完成了某些功能，必须更新相关文档，要求如下：

1. PROJECT_MASTER
    项目主控文档，跟踪项目进度，要求必须非常简洁，主要记录当前完成功能，未解决问题，风险问题等
2. DEVELOPMENT_GUIDE
    开发指南文档，包括技术栈，测试部署流程，项目功能说明等。开发指南文档，一般不会更新，我有要求再更新

# docs/my-self 

是学习文档，不要修改，也不要把该文档记录到上下文中

# refactor

重构文档，只能记录 Reference/etcd-operator 项目的分析说明文档