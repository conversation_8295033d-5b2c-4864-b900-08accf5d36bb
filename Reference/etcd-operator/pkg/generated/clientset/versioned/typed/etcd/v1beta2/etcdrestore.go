/*
Copyright 2018 The etcd-operator Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package v1beta2

import (
	v1beta2 "github.com/coreos/etcd-operator/pkg/apis/etcd/v1beta2"
	scheme "github.com/coreos/etcd-operator/pkg/generated/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// EtcdRestoresGetter has a method to return a EtcdRestoreInterface.
// A group's client should implement this interface.
type EtcdRestoresGetter interface {
	EtcdRestores(namespace string) EtcdRestoreInterface
}

// EtcdRestoreInterface has methods to work with EtcdRestore resources.
type EtcdRestoreInterface interface {
	Create(*v1beta2.EtcdRestore) (*v1beta2.EtcdRestore, error)
	Update(*v1beta2.EtcdRestore) (*v1beta2.EtcdRestore, error)
	UpdateStatus(*v1beta2.EtcdRestore) (*v1beta2.EtcdRestore, error)
	Delete(name string, options *v1.DeleteOptions) error
	DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error
	Get(name string, options v1.GetOptions) (*v1beta2.EtcdRestore, error)
	List(opts v1.ListOptions) (*v1beta2.EtcdRestoreList, error)
	Watch(opts v1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta2.EtcdRestore, err error)
	EtcdRestoreExpansion
}

// etcdRestores implements EtcdRestoreInterface
type etcdRestores struct {
	client rest.Interface
	ns     string
}

// newEtcdRestores returns a EtcdRestores
func newEtcdRestores(c *EtcdV1beta2Client, namespace string) *etcdRestores {
	return &etcdRestores{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the etcdRestore, and returns the corresponding etcdRestore object, and an error if there is any.
func (c *etcdRestores) Get(name string, options v1.GetOptions) (result *v1beta2.EtcdRestore, err error) {
	result = &v1beta2.EtcdRestore{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("etcdrestores").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do().
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of EtcdRestores that match those selectors.
func (c *etcdRestores) List(opts v1.ListOptions) (result *v1beta2.EtcdRestoreList, err error) {
	result = &v1beta2.EtcdRestoreList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("etcdrestores").
		VersionedParams(&opts, scheme.ParameterCodec).
		Do().
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested etcdRestores.
func (c *etcdRestores) Watch(opts v1.ListOptions) (watch.Interface, error) {
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("etcdrestores").
		VersionedParams(&opts, scheme.ParameterCodec).
		Watch()
}

// Create takes the representation of a etcdRestore and creates it.  Returns the server's representation of the etcdRestore, and an error, if there is any.
func (c *etcdRestores) Create(etcdRestore *v1beta2.EtcdRestore) (result *v1beta2.EtcdRestore, err error) {
	result = &v1beta2.EtcdRestore{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("etcdrestores").
		Body(etcdRestore).
		Do().
		Into(result)
	return
}

// Update takes the representation of a etcdRestore and updates it. Returns the server's representation of the etcdRestore, and an error, if there is any.
func (c *etcdRestores) Update(etcdRestore *v1beta2.EtcdRestore) (result *v1beta2.EtcdRestore, err error) {
	result = &v1beta2.EtcdRestore{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("etcdrestores").
		Name(etcdRestore.Name).
		Body(etcdRestore).
		Do().
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *etcdRestores) UpdateStatus(etcdRestore *v1beta2.EtcdRestore) (result *v1beta2.EtcdRestore, err error) {
	result = &v1beta2.EtcdRestore{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("etcdrestores").
		Name(etcdRestore.Name).
		SubResource("status").
		Body(etcdRestore).
		Do().
		Into(result)
	return
}

// Delete takes name of the etcdRestore and deletes it. Returns an error if one occurs.
func (c *etcdRestores) Delete(name string, options *v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("etcdrestores").
		Name(name).
		Body(options).
		Do().
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *etcdRestores) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("etcdrestores").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Body(options).
		Do().
		Error()
}

// Patch applies the patch and returns the patched etcdRestore.
func (c *etcdRestores) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta2.EtcdRestore, err error) {
	result = &v1beta2.EtcdRestore{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("etcdrestores").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do().
		Into(result)
	return
}
