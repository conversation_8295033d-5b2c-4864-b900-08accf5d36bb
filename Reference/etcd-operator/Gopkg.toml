[[constraint]]
  name = "k8s.io/api"
  version = "kubernetes-1.8.2"

[[constraint]]
  name = "k8s.io/apiextensions-apiserver"
  version = "kubernetes-1.8.2"

[[constraint]]
  name = "k8s.io/apimachinery"
  version = "kubernetes-1.8.2"

[[constraint]]
  name = "k8s.io/client-go"
  version = "kubernetes-1.8.2"

[[constraint]]
  name = "github.com/coreos/etcd"
  version = "3.1.9"

[[constraint]]
  name = "github.com/aws/aws-sdk-go"
  version = "1.10.9"

[[constraint]]
  name = "github.com/pborman/uuid"
  version = "1.0.0"

[[constraint]]
  name = "github.com/pkg/errors"
  version = "0.8.0"

[[constraint]]
  name = "github.com/prometheus/client_golang"
  version = "0.8.0"

[[constraint]]
  name = "github.com/sirupsen/logrus"
  version = "1.0.0"

[[constraint]]
  name = "golang.org/x/time"

[[constraint]]
  name = "github.com/Azure/azure-sdk-for-go"
  version = "v11.3.0-beta"
