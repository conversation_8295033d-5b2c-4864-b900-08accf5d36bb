# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  name = "cloud.google.com/go"
  packages = ["compute/metadata","internal"]
  revision = "3b1ae45394a234c385be014e9a488f2bb6eef821"

[[projects]]
  name = "github.com/Azure/azure-sdk-for-go"
  packages = ["storage"]
  revision = "2d1d76c9013c4feb6695a2346f0e66ea0ef77aa6"
  version = "v11.3.0-beta"

[[projects]]
  name = "github.com/Azure/go-autorest"
  packages = ["autorest","autorest/adal","autorest/azure","autorest/date"]
  revision = "809ed2ef5c4c9a60c3c2f3aa9cc11f3a7c2ce59d"
  version = "v9.6.0"

[[projects]]
  name = "github.com/PuerkitoBio/purell"
  packages = ["."]
  revision = "8a290539e2e8629dbc4e6bad948158f790ec31f4"
  version = "v1.0.0"

[[projects]]
  name = "github.com/PuerkitoBio/urlesc"
  packages = ["."]
  revision = "5bd2802263f21d8788851d5305584c82a5c75d7e"

[[projects]]
  name = "github.com/aws/aws-sdk-go"
  packages = ["aws","aws/awserr","aws/awsutil","aws/client","aws/client/metadata","aws/corehandlers","aws/credentials","aws/credentials/ec2rolecreds","aws/credentials/endpointcreds","aws/credentials/stscreds","aws/defaults","aws/ec2metadata","aws/endpoints","aws/request","aws/session","aws/signer/v4","internal/shareddefaults","private/protocol","private/protocol/query","private/protocol/query/queryutil","private/protocol/rest","private/protocol/restxml","private/protocol/xml/xmlutil","service/s3","service/s3/s3iface","service/s3/s3manager","service/sts"]
  revision = "b709581f82a77c0ff00790d1446c05719fed714d"
  version = "v1.10.9"

[[projects]]
  name = "github.com/beorn7/perks"
  packages = ["quantile"]
  revision = "3ac7bf7a47d159a033b107610db8a1b6575507a4"

[[projects]]
  name = "github.com/coreos/etcd"
  packages = ["auth/authpb","clientv3","etcdserver/api/v3rpc/rpctypes","etcdserver/etcdserverpb","mvcc/mvccpb","pkg/fileutil","pkg/tlsutil","pkg/transport"]
  revision = "0f4a535c2fb7a2920e13e2e19b9eaf6b2e9285e5"
  version = "v3.1.9"

[[projects]]
  name = "github.com/coreos/go-systemd"
  packages = ["journal"]
  revision = "48702e0da86bd25e76cfef347e2adeb434a0d0a6"
  version = "v14"

[[projects]]
  name = "github.com/coreos/pkg"
  packages = ["capnslog"]
  revision = "fa29b1d70f0beaddd4c7021607cc3c3be8ce94b8"

[[projects]]
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  revision = "782f4967f2dc4564575ca782fe2d04090b5faca8"

[[projects]]
  name = "github.com/dgrijalva/jwt-go"
  packages = ["."]
  revision = "dbeaa9332f19a944acb5736b4456cfcc02140e29"
  version = "v3.1.0"

[[projects]]
  name = "github.com/emicklei/go-restful"
  packages = [".","log"]
  revision = "ff4f55a206334ef123e4f79bbf348980da81ca46"

[[projects]]
  name = "github.com/emicklei/go-restful-swagger12"
  packages = ["."]
  revision = "dcef7f55730566d41eae5db10e7d6981829720f6"
  version = "1.0.1"

[[projects]]
  name = "github.com/ghodss/yaml"
  packages = ["."]
  revision = "73d445a93680fa1a78ae23a5839bad48f32ba1ee"

[[projects]]
  name = "github.com/go-ini/ini"
  packages = ["."]
  revision = "32e4c1e6bc4e7d0d8451aa6b75200d19e37a536a"
  version = "v1.32.0"

[[projects]]
  name = "github.com/go-openapi/jsonpointer"
  packages = ["."]
  revision = "46af16f9f7b149af66e5d1bd010e3574dc06de98"

[[projects]]
  name = "github.com/go-openapi/jsonreference"
  packages = ["."]
  revision = "13c6e3589ad90f49bd3e3bbe2c2cb3d7a4142272"

[[projects]]
  name = "github.com/go-openapi/spec"
  packages = ["."]
  revision = "6aced65f8501fe1217321abf0749d354824ba2ff"

[[projects]]
  name = "github.com/go-openapi/swag"
  packages = ["."]
  revision = "1d0bd113de87027671077d3c71eb3ac5d7dbba72"

[[projects]]
  name = "github.com/gogo/protobuf"
  packages = ["proto","sortkeys"]
  revision = "c0656edd0d9eab7c66d1eb0c568f9039345796f7"

[[projects]]
  name = "github.com/golang/glog"
  packages = ["."]
  revision = "44145f04b68cf362d9c4df2182967c2275eaefed"

[[projects]]
  name = "github.com/golang/groupcache"
  packages = ["lru"]
  revision = "02826c3e79038b59d737d3b1c0a1d937f71a4433"

[[projects]]
  name = "github.com/golang/protobuf"
  packages = ["jsonpb","proto","ptypes","ptypes/any","ptypes/duration","ptypes/timestamp"]
  revision = "4bd1920723d7b7c925de087aa32e2187708897f7"

[[projects]]
  name = "github.com/google/btree"
  packages = ["."]
  revision = "7d79101e329e5a3adf994758c578dab82b90c017"

[[projects]]
  name = "github.com/google/gofuzz"
  packages = ["."]
  revision = "44d81051d367757e1c7c6a5a86423ece9afcf63c"

[[projects]]
  name = "github.com/googleapis/gnostic"
  packages = ["OpenAPIv2","compiler","extensions"]
  revision = "0c5108395e2debce0d731cf0287ddf7242066aba"

[[projects]]
  name = "github.com/gregjones/httpcache"
  packages = [".","diskcache"]
  revision = "787624de3eb7bd915c329cba748687a3b22666a6"

[[projects]]
  name = "github.com/grpc-ecosystem/go-grpc-prometheus"
  packages = ["."]
  revision = "6b7015e65d366bf3f19b2b2a000a831940f0f7e0"
  version = "v1.1"

[[projects]]
  name = "github.com/grpc-ecosystem/grpc-gateway"
  packages = ["runtime","runtime/internal","utilities"]
  revision = "84398b94e188ee336f307779b57b3aa91af7063c"

[[projects]]
  name = "github.com/hashicorp/golang-lru"
  packages = [".","simplelru"]
  revision = "a0d98a5f288019575c6d1f4bb1573fef2d1fcdc4"

[[projects]]
  branch = "master"
  name = "github.com/howeyc/gopass"
  packages = ["."]
  revision = "bf9dde6d0d2c004a008c27aaee91170c786f6db8"

[[projects]]
  name = "github.com/imdario/mergo"
  packages = ["."]
  revision = "6633656539c1639d9d78127b7d47c622b5d7b6dc"

[[projects]]
  branch = "master"
  name = "github.com/jmespath/go-jmespath"
  packages = ["."]
  revision = "dd801d4f4ce7ac746e7e7b4489d2fa600b3b096b"

[[projects]]
  name = "github.com/json-iterator/go"
  packages = ["."]
  revision = "36b14963da70d11297d313183d7e6388c8510e1e"
  version = "1.0.0"

[[projects]]
  name = "github.com/juju/ratelimit"
  packages = ["."]
  revision = "5b9ff866471762aa2ab2dced63c9fb6f53921342"

[[projects]]
  name = "github.com/mailru/easyjson"
  packages = ["buffer","jlexer","jwriter"]
  revision = "d5b7844b561a7bc640052f1b935f7b800330d7e0"

[[projects]]
  name = "github.com/matttproud/golang_protobuf_extensions"
  packages = ["pbutil"]
  revision = "fc2b8d3a73c4867e51861bbdd5ae3c1f0869dd6a"

[[projects]]
  name = "github.com/pborman/uuid"
  packages = ["."]
  revision = "a97ce2ca70fa5a848076093f05e639a89ca34d06"
  version = "v1.0"

[[projects]]
  branch = "master"
  name = "github.com/petar/GoLLRB"
  packages = ["llrb"]
  revision = "53be0d36a84c2a886ca057d34b6aa4468df9ccb4"

[[projects]]
  name = "github.com/peterbourgon/diskv"
  packages = ["."]
  revision = "5f041e8faa004a95c88a202771f4cc3e991971e6"
  version = "v2.0.1"

[[projects]]
  name = "github.com/pkg/errors"
  packages = ["."]
  revision = "645ef00459ed84a119197bfb8d8205042c6df63d"
  version = "v0.8.0"

[[projects]]
  name = "github.com/prometheus/client_golang"
  packages = ["prometheus"]
  revision = "c5b7fccd204277076155f10851dad72b76a49317"
  version = "v0.8.0"

[[projects]]
  name = "github.com/prometheus/client_model"
  packages = ["go"]
  revision = "fa8ad6fec33561be4280a8f0514318c79d7f6cb6"

[[projects]]
  name = "github.com/prometheus/common"
  packages = ["expfmt","internal/bitbucket.org/ww/goautoneg","model"]
  revision = "13ba4ddd0caa9c28ca7b7bffe1dfa9ed8d5ef207"

[[projects]]
  name = "github.com/prometheus/procfs"
  packages = [".","xfs"]
  revision = "65c1f6f8f0fc1e2185eb9863a3bc751496404259"

[[projects]]
  name = "github.com/satori/uuid"
  packages = ["."]
  revision = "879c5887cd475cd7864858769793b2ceb0d44feb"
  version = "v1.1.0"

[[projects]]
  name = "github.com/sirupsen/logrus"
  packages = ["."]
  revision = "202f25545ea4cf9b191ff7f846df5d87c9382c2b"
  version = "v1.0.0"

[[projects]]
  name = "github.com/spf13/pflag"
  packages = ["."]
  revision = "9ff6c6923cfffbcd502984b8e0c80539a94968b7"

[[projects]]
  name = "golang.org/x/crypto"
  packages = ["ssh/terminal"]
  revision = "81e90905daefcd6fd217b62423c0908922eadb30"

[[projects]]
  name = "golang.org/x/net"
  packages = ["context","context/ctxhttp","http2","http2/hpack","idna","internal/timeseries","lex/httplex","trace"]
  revision = "1c05540f6879653db88113bc4a2b70aec4bd491f"

[[projects]]
  name = "golang.org/x/oauth2"
  packages = [".","google","internal","jws","jwt"]
  revision = "a6bd8cefa1811bd24b86f8902872e4e8225f74c4"

[[projects]]
  name = "golang.org/x/sys"
  packages = ["unix","windows"]
  revision = "7ddbeae9ae08c6a06a59597f0c9edbc5ff2444ce"

[[projects]]
  name = "golang.org/x/text"
  packages = ["cases","internal","internal/gen","internal/tag","internal/triegen","internal/ucd","language","runes","secure/bidirule","secure/precis","transform","unicode/bidi","unicode/cldr","unicode/norm","unicode/rangetable","width"]
  revision = "b19bf474d317b857955b12035d2c5acb57ce8b01"

[[projects]]
  branch = "master"
  name = "golang.org/x/time"
  packages = ["rate"]
  revision = "6dc17368e09b0e8634d71cac8168d853e869a0c7"

[[projects]]
  branch = "master"
  name = "google.golang.org/appengine"
  packages = [".","internal","internal/app_identity","internal/base","internal/datastore","internal/log","internal/modules","internal/remote_api","internal/urlfetch","urlfetch"]
  revision = "9d8544a6b2c7df9cff240fcf92d7b2f59bc13416"

[[projects]]
  name = "google.golang.org/grpc"
  packages = [".","codes","credentials","grpclog","internal","metadata","naming","peer","transport"]
  revision = "777daa17ff9b5daef1cfdf915088a2ada3332bf0"
  version = "v1.0.4"

[[projects]]
  name = "gopkg.in/inf.v0"
  packages = ["."]
  revision = "3887ee99ecf07df5b447e9b00d9c0b2adaa9f3e4"
  version = "v0.9.0"

[[projects]]
  name = "gopkg.in/yaml.v2"
  packages = ["."]
  revision = "53feefa2559fb8dfa8d81baad31be332c97d6c77"

[[projects]]
  name = "k8s.io/api"
  packages = ["admissionregistration/v1alpha1","apps/v1beta1","apps/v1beta2","authentication/v1","authentication/v1beta1","authorization/v1","authorization/v1beta1","autoscaling/v1","autoscaling/v2beta1","batch/v1","batch/v1beta1","batch/v2alpha1","certificates/v1beta1","core/v1","extensions/v1beta1","networking/v1","policy/v1beta1","rbac/v1","rbac/v1alpha1","rbac/v1beta1","scheduling/v1alpha1","settings/v1alpha1","storage/v1","storage/v1beta1"]
  revision = "4df58c811fe2e65feb879227b2b245e4dc26e7ad"
  version = "kubernetes-1.8.2"

[[projects]]
  name = "k8s.io/apiextensions-apiserver"
  packages = ["pkg/apis/apiextensions","pkg/apis/apiextensions/v1beta1","pkg/client/clientset/clientset","pkg/client/clientset/clientset/scheme","pkg/client/clientset/clientset/typed/apiextensions/v1beta1"]
  revision = "e509bb64fe1116e12a32273a2032426aa1a5fd26"
  version = "kubernetes-1.8.2"

[[projects]]
  name = "k8s.io/apimachinery"
  packages = ["pkg/api/equality","pkg/api/errors","pkg/api/meta","pkg/api/resource","pkg/apis/meta/internalversion","pkg/apis/meta/v1","pkg/apis/meta/v1/unstructured","pkg/apis/meta/v1alpha1","pkg/conversion","pkg/conversion/queryparams","pkg/conversion/unstructured","pkg/fields","pkg/labels","pkg/runtime","pkg/runtime/schema","pkg/runtime/serializer","pkg/runtime/serializer/json","pkg/runtime/serializer/protobuf","pkg/runtime/serializer/recognizer","pkg/runtime/serializer/streaming","pkg/runtime/serializer/versioning","pkg/selection","pkg/types","pkg/util/cache","pkg/util/clock","pkg/util/diff","pkg/util/errors","pkg/util/framer","pkg/util/intstr","pkg/util/json","pkg/util/mergepatch","pkg/util/net","pkg/util/rand","pkg/util/runtime","pkg/util/sets","pkg/util/strategicpatch","pkg/util/validation","pkg/util/validation/field","pkg/util/wait","pkg/util/yaml","pkg/version","pkg/watch","third_party/forked/golang/json","third_party/forked/golang/reflect"]
  revision = "019ae5ada31de202164b118aee88ee2d14075c31"
  version = "kubernetes-1.8.2"

[[projects]]
  name = "k8s.io/client-go"
  packages = ["discovery","discovery/fake","kubernetes","kubernetes/fake","kubernetes/scheme","kubernetes/typed/admissionregistration/v1alpha1","kubernetes/typed/admissionregistration/v1alpha1/fake","kubernetes/typed/apps/v1beta1","kubernetes/typed/apps/v1beta1/fake","kubernetes/typed/apps/v1beta2","kubernetes/typed/apps/v1beta2/fake","kubernetes/typed/authentication/v1","kubernetes/typed/authentication/v1/fake","kubernetes/typed/authentication/v1beta1","kubernetes/typed/authentication/v1beta1/fake","kubernetes/typed/authorization/v1","kubernetes/typed/authorization/v1/fake","kubernetes/typed/authorization/v1beta1","kubernetes/typed/authorization/v1beta1/fake","kubernetes/typed/autoscaling/v1","kubernetes/typed/autoscaling/v1/fake","kubernetes/typed/autoscaling/v2beta1","kubernetes/typed/autoscaling/v2beta1/fake","kubernetes/typed/batch/v1","kubernetes/typed/batch/v1/fake","kubernetes/typed/batch/v1beta1","kubernetes/typed/batch/v1beta1/fake","kubernetes/typed/batch/v2alpha1","kubernetes/typed/batch/v2alpha1/fake","kubernetes/typed/certificates/v1beta1","kubernetes/typed/certificates/v1beta1/fake","kubernetes/typed/core/v1","kubernetes/typed/core/v1/fake","kubernetes/typed/extensions/v1beta1","kubernetes/typed/extensions/v1beta1/fake","kubernetes/typed/networking/v1","kubernetes/typed/networking/v1/fake","kubernetes/typed/policy/v1beta1","kubernetes/typed/policy/v1beta1/fake","kubernetes/typed/rbac/v1","kubernetes/typed/rbac/v1/fake","kubernetes/typed/rbac/v1alpha1","kubernetes/typed/rbac/v1alpha1/fake","kubernetes/typed/rbac/v1beta1","kubernetes/typed/rbac/v1beta1/fake","kubernetes/typed/scheduling/v1alpha1","kubernetes/typed/scheduling/v1alpha1/fake","kubernetes/typed/settings/v1alpha1","kubernetes/typed/settings/v1alpha1/fake","kubernetes/typed/storage/v1","kubernetes/typed/storage/v1/fake","kubernetes/typed/storage/v1beta1","kubernetes/typed/storage/v1beta1/fake","pkg/version","plugin/pkg/client/auth/gcp","rest","rest/watch","testing","third_party/forked/golang/template","tools/auth","tools/cache","tools/clientcmd","tools/clientcmd/api","tools/clientcmd/api/latest","tools/clientcmd/api/v1","tools/leaderelection","tools/leaderelection/resourcelock","tools/metrics","tools/pager","tools/record","tools/reference","transport","util/cert","util/flowcontrol","util/homedir","util/integer","util/jsonpath","util/workqueue"]
  revision = "35ccd4336052e7d73018b1382413534936f34eee"
  version = "kubernetes-1.8.2"

[[projects]]
  name = "k8s.io/kube-openapi"
  packages = ["pkg/common"]
  revision = "868f2f29720b192240e18284659231b440f9cda5"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  inputs-digest = "348ce70c891e3e31857fd45276c70cf2f54e6a63b76b889ee79525d8e8ea86a0"
  solver-name = "gps-cdcl"
  solver-version = 1
